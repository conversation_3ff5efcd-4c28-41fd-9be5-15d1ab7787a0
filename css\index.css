* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}

body {
  background-color: #f3f5f7;
}

li {
  list-style: none;
}
a {
  text-decoration: none;
}

/* 版心 */
.wrapper {
  width: 1200px;
  margin: 0 auto;
}

/* 头部 */
.header {
  height: 42px;
  /* background-color: pink; */
  margin-top: 30px;
}

h1 {
  float: left;
}

/* 导航 */
.nav {
  float: left;
  margin-left: 70px;
  height: 42px;
}

.nav li {
  float: left;
  margin-right: 26px;
  list-style: none;
}

.nav li a {
  display: inline-block;
  padding: 0 9px;
  height: 42px;
  line-height: 42px;
  text-decoration: none;
}

.nav li a:hover {
  border-bottom: 2px solid #00a4ff;
}

.header .search input {
  float: left;
  width: 372px;
  height: 40px;
  border: solid 1px #00a4ff;
  border-right: 0;
}

.header .search input::placeholder {
  padding-left: 21px;
  font-size: 14px;
  color: #bfbfbf;
}

.header .search button {
  float: left;
  width: 51px;
  height: 42px;
  border: 0;
}

/* 个人信息 */
.user img {
  vertical-align: middle;
}

.user {
  float: left;
  margin-left: 31px;
  line-height: 42px;
  font-size: 14px;
  color: #666666;
}

/* 轮播 */

.banner {
  margin-top: 29px;
  height: 420px;
  background-color: #1c036c;
}

.banner .wrapper {
  height: 420px;
  background-image: url(../images/banner2.png);
}

.banner .left {
  float: left;
  padding: 0 20px;
  width: 190px;
  height: 420px;
  line-height: 44px;
  background-color: #13014b;
}

.banner .right {
  float: right;
  margin-top: 50px;
  width: 228px;
  height: 300px;
  background-color: #ffffff;
}

.banner .left li {
  list-style: none;
}

.banner .left li a {
  color: #fafafb;
  font-size: 14px;
  text-decoration: none;
}

.banner .left li a span {
  float: right;
  margin-right: 21px;
}

.banner .left li a:hover {
  color: #0386cb;
}

.right h2 {
  width: 228px;
  height: 48px;
  font-size: 18px;
  color: #f3f9fc;
  background-color: #9bceea;
  line-height: 48px;
  text-align: center;
}

.banner .right .content {
  padding: 0 18px;
}

.banner .right .content dl {
  padding: 10px 0;
  border-bottom: 2px solid #e5e5e5;
}

.banner .right .content dt {
  font-size: 16px;
  color: #4e4e4e;
}

.banner .right .content dd {
  font-size: 14px;
  color: #4e4e4e;
}

.banner .right .more {
  display: block;
  margin: 4px auto;
  width: 200px;
  height: 41px;
  text-align: center;
  text-decoration: none;
  line-height: 39px;
  border: solid 1px #00a4ff;
  color: #00a4ff;
}

.goods {
  margin: 0 auto;
  margin-top: 8px;
  width: 1200px;
  height: 60px;
  background-color: #ffffff;
  box-shadow: 0px 2px 3px 0px rgba(118, 118, 118, 0.2);
}

/* 精品推荐 */
.goods h3 {
  float: left;
  margin-left: 34px;
  margin-right: 34px;
  color: #00a4ff;
  line-height: 60px;
}

.goods ul li {
  list-style: none;
}

.goods ul li a {
  float: left;
  padding: 0 30px;
  border-right: solid 1px #bfbfbf;
  text-decoration: none;
  margin-top: 18px;
}
.goods ul li a:first-child {
  border-left: solid 1px #bfbfbf;
}

.goods ul li a:last-child {
  border-right: 0;
}

.goods .more {
  float: right;
  margin-right: 26px;
  font-size: 14px;
  color: #00a4ff;
  text-decoration: none;
  line-height: 60px;
}

/* 精品推荐内容 */
.box {
  margin-top: 35px;
}

.box .title {
  height: 40px;
}

.box .title h2 {
  float: left;
  font-size: 20px;
  color: #494949;
  font-weight: 400;
}

.box .title a {
  float: right;
  margin-right: 30px;
  font-size: 12px;
  color: #a5a5a5;
}

.box .content li {
  float: left;
  margin-right: 15px;
  margin-bottom: 15px;
  width: 228px;
  height: 270px;
  background-color: #fff;
}

.box .content li:nth-child(5n) {
  margin-right: 0;
}

.box .content li h3 {
  padding: 20px;
  font-size: 14px;
  color: #050505;
  font-weight: 400;
}

.box .content li p {
  padding: 0 20px;
  font-size: 12px;
  color: #999;
}

.box .content li span {
  color: #ff7c2d;
}

/* 编程入门新样式 */
.programming-section {
  margin-top: 50px;
  padding: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.main-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin: 0;
  position: relative;
  display: inline-block;
}

.title-decoration {
  position: absolute;
  top: -10px;
  right: -40px;
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.dot-1 {
  background: #ff6b6b;
  animation-delay: 0s;
}

.dot-2 {
  background: #4ecdc4;
  animation-delay: 0.3s;
}

.dot-3 {
  background: #45b7d1;
  animation-delay: 0.6s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

.python-text {
  color: #3776ab;
  position: relative;
}

.python-text::after {
  content: '';
  position: absolute;
  top: -10px;
  right: -20px;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #ff6b6b, #ff8e8e);
  border-radius: 50%;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.nav-tabs ul {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-tabs ul li a {
  text-decoration: none;
  color: #666;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.nav-tabs ul li a.active,
.nav-tabs ul li a:hover {
  color: #00a4ff;
  background-color: rgba(0, 164, 255, 0.1);
}

.programming-content {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

/* PHP入门卡片样式 */
.php-intro-card {
  flex: 0 0 350px;
  height: 450px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px 30px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.php-intro-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.card-content {
  position: relative;
  z-index: 2;
}

.php-intro-card h2 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #fff;
}

.php-intro-card h3 {
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 40px;
  color: rgba(255, 255, 255, 0.9);
}

.learning-path {
  margin-top: 30px;
}

.step {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  opacity: 0;
  animation: slideInLeft 0.6s ease forwards;
}

.step:nth-child(1) { animation-delay: 0.2s; }
.step:nth-child(2) { animation-delay: 0.4s; }
.step:nth-child(3) { animation-delay: 0.6s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  font-size: 16px;
  margin-right: 15px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.step:hover .step-icon {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
}

.step-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.float-icon {
  position: absolute;
  font-size: 20px;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.float-1 {
  top: 20%;
  right: 15%;
  animation-delay: 0s;
}

.float-2 {
  top: 60%;
  right: 25%;
  animation-delay: 1.5s;
}

.float-3 {
  top: 35%;
  right: 5%;
  animation-delay: 3s;
}

.float-4 {
  top: 75%;
  right: 10%;
  animation-delay: 4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(5deg);
  }
  50% {
    transform: translateY(-20px) rotate(0deg);
  }
  75% {
    transform: translateY(-10px) rotate(-5deg);
  }
}

.building-icons {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.building {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px 4px 0 0;
}

.building-1 {
  width: 20px;
  height: 40px;
}

.building-2 {
  width: 25px;
  height: 60px;
}

.building-3 {
  width: 18px;
  height: 35px;
}

/* 课程网格样式 */
.course-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.course-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.course-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.course-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
  transform: scale(1.05);
}

.course-info {
  padding: 20px;
}

.course-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-info p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.level {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .wrapper {
    width: 95%;
    padding: 0 20px;
  }

  .programming-content {
    flex-direction: column;
  }

  .php-intro-card {
    flex: none;
    width: 100%;
    margin-bottom: 30px;
  }

  .course-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-title {
    font-size: 28px;
  }

  .section-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .nav-tabs ul {
    justify-content: center;
  }

  .php-intro-card {
    height: auto;
    padding: 30px 20px;
  }

  .course-grid {
    grid-template-columns: 1fr;
  }
}

/* 添加一些动画效果 */
.course-card {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
}

.course-card:nth-child(1) { animation-delay: 0.1s; }
.course-card:nth-child(2) { animation-delay: 0.2s; }
.course-card:nth-child(3) { animation-delay: 0.3s; }
.course-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加装饰性元素 */
.programming-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00a4ff, transparent);
  opacity: 0.5;
}

.programming-section {
  position: relative;
}
