* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}

body {
  background-color: #f3f5f7;
}

li {
  list-style: none;
}
a {
  text-decoration: none;
}

/* 版心 */
.wrapper {
  width: 1200px;
  margin: 0 auto;
}

/* 头部 */
.header {
  height: 42px;
  /* background-color: pink; */
  margin-top: 30px;
}

h1 {
  float: left;
}

/* 导航 */
.nav {
  float: left;
  margin-left: 70px;
  height: 42px;
}

.nav li {
  float: left;
  margin-right: 26px;
  list-style: none;
}

.nav li a {
  display: inline-block;
  padding: 0 9px;
  height: 42px;
  line-height: 42px;
  text-decoration: none;
}

.nav li a:hover {
  border-bottom: 2px solid #00a4ff;
}

.header .search input {
  float: left;
  width: 372px;
  height: 40px;
  border: solid 1px #00a4ff;
  border-right: 0;
}

.header .search input::placeholder {
  padding-left: 21px;
  font-size: 14px;
  color: #bfbfbf;
}

.header .search button {
  float: left;
  width: 51px;
  height: 42px;
  border: 0;
}

/* 个人信息 */
.user img {
  vertical-align: middle;
}

.user {
  float: left;
  margin-left: 31px;
  line-height: 42px;
  font-size: 14px;
  color: #666666;
}

/* 轮播 */

.banner {
  margin-top: 29px;
  height: 420px;
  background-color: #1c036c;
}

.banner .wrapper {
  height: 420px;
  background-image: url(../images/banner2.png);
}

.banner .left {
  float: left;
  padding: 0 20px;
  width: 190px;
  height: 420px;
  line-height: 44px;
  background-color: #13014b;
}

.banner .right {
  float: right;
  margin-top: 50px;
  width: 228px;
  height: 300px;
  background-color: #ffffff;
}

.banner .left li {
  list-style: none;
}

.banner .left li a {
  color: #fafafb;
  font-size: 14px;
  text-decoration: none;
}

.banner .left li a span {
  float: right;
  margin-right: 21px;
}

.banner .left li a:hover {
  color: #0386cb;
}

.right h2 {
  width: 228px;
  height: 48px;
  font-size: 18px;
  color: #f3f9fc;
  background-color: #9bceea;
  line-height: 48px;
  text-align: center;
}

.banner .right .content {
  padding: 0 18px;
}

.banner .right .content dl {
  padding: 10px 0;
  border-bottom: 2px solid #e5e5e5;
}

.banner .right .content dt {
  font-size: 16px;
  color: #4e4e4e;
}

.banner .right .content dd {
  font-size: 14px;
  color: #4e4e4e;
}

.banner .right .more {
  display: block;
  margin: 4px auto;
  width: 200px;
  height: 41px;
  text-align: center;
  text-decoration: none;
  line-height: 39px;
  border: solid 1px #00a4ff;
  color: #00a4ff;
}

.goods {
  margin: 0 auto;
  margin-top: 8px;
  width: 1200px;
  height: 60px;
  background-color: #ffffff;
  box-shadow: 0px 2px 3px 0px rgba(118, 118, 118, 0.2);
}

/* 精品推荐 */
.goods h3 {
  float: left;
  margin-left: 34px;
  margin-right: 34px;
  color: #00a4ff;
  line-height: 60px;
}

.goods ul li {
  list-style: none;
}

.goods ul li a {
  float: left;
  padding: 0 30px;
  border-right: solid 1px #bfbfbf;
  text-decoration: none;
  margin-top: 18px;
}
.goods ul li a:first-child {
  border-left: solid 1px #bfbfbf;
}

.goods ul li a:last-child {
  border-right: 0;
}

.goods .more {
  float: right;
  margin-right: 26px;
  font-size: 14px;
  color: #00a4ff;
  text-decoration: none;
  line-height: 60px;
}

/* 精品推荐内容 */
.box {
  margin-top: 35px;
}

.box .title {
  height: 40px;
}

.box .title h2 {
  float: left;
  font-size: 20px;
  color: #494949;
  font-weight: 400;
}

.box .title a {
  float: right;
  margin-right: 30px;
  font-size: 12px;
  color: #a5a5a5;
}

.box .content li {
  float: left;
  margin-right: 15px;
  margin-bottom: 15px;
  width: 228px;
  height: 270px;
  background-color: #fff;
}

.box .content li:nth-child(5n) {
  margin-right: 0;
}

.box .content li h3 {
  padding: 20px;
  font-size: 14px;
  color: #050505;
  font-weight: 400;
}

.box .content li p {
  padding: 0 20px;
  font-size: 12px;
  color: #999;
}

.box .content li span {
  color: #ff7c2d;
}

/* 编程入门 */
.box1 {
  margin-top: 38px;
}

.box1 .title {
  height: 40px;
}

.box1 .title h2 {
  float: left;
  font-size: 20px;
  color: #494949;
}

.box1 .title ul {
  float: left;
  margin-left: 360px;
  width: 338px;
  height: 15px;
}
.box1 .title ul li a {
  float: left;
  color: #868686;
}

.box1 .title ul li a:hover {
  color: #00a4ff;
}

.box1 .title a {
  float: right;
  margin-right: 40px;
  margin-top: 5px;
  font-size: 12px;
  color: #a5a5a5;
}

.box1 .content .left {
  float: left;
  width: 228px;
  height: 392px;
}
